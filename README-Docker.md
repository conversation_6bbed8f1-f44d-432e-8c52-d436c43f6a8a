# Odoo 14.0 Docker Deployment

This project contains a complete Odoo 14.0 setup with custom modules ready for Docker deployment.

## Project Structure

- **Odoo Core**: Standard Odoo 14.0 installation
- **Custom Modules**: Platform ODEX25 Light with specialized modules:
  - Accounting, BI, Fleet Management
  - HR, Project Management
  - Real Estate, Transactions
  - DMS, Helpdesk, Maintenance
  - Purchase, Sales, Website
  - OpenEduCat ERP integration
- **Production Data**: File storage and attachments
- **Configuration**: Optimized for Docker environment

## Prerequisites

- Docker Desktop installed and running
- At least 4GB RAM available for containers
- 10GB free disk space

## Quick Start

1. **Build and start the containers:**
   ```bash
   docker-compose up --build -d
   ```

2. **Check container status:**
   ```bash
   docker-compose ps
   ```

3. **View logs:**
   ```bash
   docker-compose logs -f odoo
   ```

4. **Access Odoo:**
   - URL: http://localhost:7981
   - Admin Password: `5XdF*CVsdb2$3%dsei5I$`
   - Database: `odoo_production`

## Configuration Details

### Ports
- **7981**: Main Odoo web interface
- **7982**: Longpolling (real-time features)
- **5432**: PostgreSQL database

### Database
- **Host**: postgres (container name)
- **Database**: odoo_production
- **User**: odooprod
- **Password**: odooprod

### Volumes
- `postgres_data`: PostgreSQL data persistence
- `odoo_data`: Odoo file storage and attachments
- `odoo_addons`: Custom addon modules

## Management Commands

### Start services
```bash
docker-compose up -d
```

### Stop services
```bash
docker-compose down
```

### Restart Odoo only
```bash
docker-compose restart odoo
```

### View real-time logs
```bash
docker-compose logs -f
```

### Access Odoo container shell
```bash
docker-compose exec odoo bash
```

### Access PostgreSQL
```bash
docker-compose exec postgres psql -U odooprod -d odoo_production
```

## Troubleshooting

### Container won't start
1. Check Docker Desktop is running
2. Ensure ports 7981, 7982, 5432 are not in use
3. Check logs: `docker-compose logs`

### Database connection issues
1. Wait for PostgreSQL to fully initialize (30-60 seconds)
2. Check database health: `docker-compose exec postgres pg_isready`

### Performance issues
1. Increase Docker Desktop memory allocation to 4GB+
2. Adjust worker settings in `odoo-server-docker.conf`

### Reset everything
```bash
docker-compose down -v
docker-compose up --build -d
```

## Customization

### Modify Odoo configuration
Edit `odoo-server-docker.conf` and restart:
```bash
docker-compose restart odoo
```

### Add custom modules
1. Place modules in a local directory
2. Add volume mount in `docker-compose.yml`
3. Update `addons_path` in configuration

### Database backup
```bash
docker-compose exec postgres pg_dump -U odooprod odoo_production > backup.sql
```

### Database restore
```bash
docker-compose exec -T postgres psql -U odooprod odoo_production < backup.sql
```

## Security Notes

- Change default admin password in production
- Use environment variables for sensitive data
- Configure proper firewall rules
- Regular database backups recommended

## Support

For issues related to:
- **Docker setup**: Check Docker Desktop documentation
- **Odoo functionality**: Refer to Odoo 14.0 documentation
- **Custom modules**: Contact your module provider
