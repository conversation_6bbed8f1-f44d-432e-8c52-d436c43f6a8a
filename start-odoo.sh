#!/bin/bash

echo "========================================"
echo "   Starting Odoo 14.0 in Docker Desktop"
echo "========================================"
echo

echo "Checking Docker Desktop..."
if ! command -v docker &> /dev/null; then
    echo "ERROR: Docker Desktop is not running!"
    echo "Please start Docker Desktop and try again."
    exit 1
fi

echo "Docker Desktop is running ✓"
echo

echo "Building and starting containers..."
docker-compose up --build -d

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to start containers!"
    echo "Check Docker Desktop for error details."
    exit 1
fi

echo
echo "========================================"
echo "   Odoo is starting up!"
echo "========================================"
echo
echo "Please wait 2-3 minutes for full startup..."
echo
echo "Access your Odoo at: http://localhost:8069"
echo "Admin Password: 5XdF*CVsdb2$3%dsei5I$"
echo "Database: odoo_production"
echo
echo "To view logs: docker-compose logs -f odoo"
echo "To stop: docker-compose down"
echo

# Try to open Docker Desktop (macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "Opening Docker Desktop..."
    open -a "Docker Desktop"
    
    echo
    echo "Press Enter to open Odoo in browser..."
    read
    open "http://localhost:8069"
else
    echo "Please open Docker Desktop manually to monitor containers."
    echo "Then visit: http://localhost:8069"
fi
