FROM python:3.8-slim-bullseye

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    python3-dev \
    libxml2-dev \
    libxslt1-dev \
    libldap2-dev \
    libsasl2-dev \
    libtiff5-dev \
    libjpeg62-turbo-dev \
    libopenjp2-7-dev \
    zlib1g-dev \
    libfreetype6-dev \
    libpq-dev \
    wkhtmltopdf \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create odoo user
RUN useradd -ms /bin/bash odoo

# Set working directory
WORKDIR /opt/odoo

# Copy and extract Odoo core
COPY odoo-14.0-core.tar.gz .
RUN tar -xzf odoo-14.0-core.tar.gz && rm odoo-14.0-core.tar.gz

# Copy and extract custom addons
COPY platform-odex25-light.tar.gz .
RUN tar -xzf platform-odex25-light.tar.gz && rm platform-odex25-light.tar.gz

# Copy and extract data directory
COPY prod_data_dir.tar.gz .
RUN tar -xzf prod_data_dir.tar.gz && rm prod_data_dir.tar.gz

# Install Python dependencies
RUN pip install --upgrade pip
RUN pip install -r odoo-14.0/requirements.txt

# Additional Python packages that might be needed
RUN pip install psycopg2-binary

# Copy configuration file
COPY odoo-server.conf /etc/odoo-server.conf

# Set proper permissions
RUN chown -R odoo:odoo /opt/odoo
RUN chown odoo:odoo /etc/odoo-server.conf

# Switch to odoo user
USER odoo

# Expose ports
EXPOSE 7981 7982

# Set the default command
CMD ["/opt/odoo/odoo-14.0/odoo-bin", "-c", "/etc/odoo-server.conf"]
