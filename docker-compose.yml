version: '3.8'

services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: odoo_production
      POSTGRES_USER: odooprod
      POSTGRES_PASSWORD: odooprod
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U odooprod -d odoo_production"]
      interval: 30s
      timeout: 10s
      retries: 5

  odoo:
    build: .
    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "7981:7981"  # Main Odoo port
      - "7982:7982"  # Longpolling port
    volumes:
      - odoo_data:/opt/odoo/prod_data_dir
      - odoo_addons:/opt/odoo/platform-odex25-light
      - ./odoo-server-docker.conf:/etc/odoo-server.conf
    environment:
      - HOST=postgres
      - USER=odooprod
      - PASSWORD=odooprod
    restart: unless-stopped
    command: ["/opt/odoo/odoo-14.0/odoo-bin", "-c", "/etc/odoo-server.conf", "--db-filter=odoo_production"]

volumes:
  postgres_data:
  odoo_data:
  odoo_addons:
