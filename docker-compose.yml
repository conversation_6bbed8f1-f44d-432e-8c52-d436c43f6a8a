version: '3.8'

services:
  postgres:
    image: postgres:13
    container_name: odoo_postgres
    environment:
      POSTGRES_DB: odoo_production
      POSTGRES_USER: odooprod
      POSTGRES_PASSWORD: odooprod
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - odoo_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U odooprod -d odoo_production"]
      interval: 30s
      timeout: 10s
      retries: 5

  odoo:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: odoo_app
    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "8069:7981"  # Map to standard Odoo port for easier access
      - "8072:7982"  # Longpolling port
    volumes:
      - odoo_data:/opt/odoo/prod_data_dir
      - ./odoo-server-docker.conf:/etc/odoo-server.conf:ro
    environment:
      - HOST=postgres
      - USER=odooprod
      - PASSWORD=odooprod
    restart: unless-stopped
    networks:
      - odoo_network
    command: ["/opt/odoo/odoo-14.0/odoo-bin", "-c", "/etc/odoo-server.conf"]

volumes:
  postgres_data:
    driver: local
  odoo_data:
    driver: local

networks:
  odoo_network:
    driver: bridge
