@echo off
echo ========================================
echo    Starting Odoo 14.0 in Docker Desktop
echo ========================================
echo.

echo Checking Docker Desktop...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker Desktop is not running!
    echo Please start Docker Desktop and try again.
    pause
    exit /b 1
)

echo Docker Desktop is running ✓
echo.

echo Building and starting containers...
docker-compose up --build -d

if errorlevel 1 (
    echo ERROR: Failed to start containers!
    echo Check Docker Desktop for error details.
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Odoo is starting up!
echo ========================================
echo.
echo Please wait 2-3 minutes for full startup...
echo.
echo Access your Odoo at: http://localhost:8069
echo Admin Password: 5XdF*CVsdb2$3%dsei5I$
echo Database: odoo_production
echo.
echo To view logs: docker-compose logs -f odoo
echo To stop: docker-compose down
echo.
echo Opening Docker Desktop...
start "" "docker-desktop:"

echo.
echo Press any key to open Odoo in browser...
pause >nul
start "" "http://localhost:8069"
