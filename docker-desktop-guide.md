# 🐳 Odoo 14.0 Docker Desktop Deployment Guide

## Step-by-Step Instructions for Docker Desktop

### Prerequisites ✅
- Docker Desktop installed and running
- At least 4GB RAM allocated to Docker Desktop
- 10GB free disk space

### Step 1: Prepare Docker Desktop
1. Open **Docker Desktop**
2. Go to **Settings** → **Resources** → **Advanced**
3. Set **Memory** to at least **4GB**
4. Set **CPUs** to **2** or more
5. Click **Apply & Restart**

### Step 2: Build and Deploy
1. Open **Terminal** or **Command Prompt**
2. Navigate to your project folder:
   ```bash
   cd "/Users/<USER>/Desktop/نسخة من odoo-14.0-complete-backup"
   ```

3. Build and start containers:
   ```bash
   docker-compose up --build -d
   ```

### Step 3: Monitor in Docker Desktop
1. Open **Docker Desktop**
2. Go to **Containers** tab
3. You should see:
   - `odoo_postgres` (PostgreSQL database)
   - `odoo_app` (Odoo application)

### Step 4: Access Your Odoo
- **URL**: http://localhost:8069
- **Admin Password**: `5XdF*CVsdb2$3%dsei5I$`
- **Database**: `odoo_production`

### Step 5: Using Docker Desktop Interface

#### View Container Logs
1. Click on container name in Docker Desktop
2. Go to **Logs** tab
3. Monitor startup progress

#### Container Actions
- **Start/Stop**: Use play/stop buttons
- **Restart**: Right-click → Restart
- **Shell Access**: Click **CLI** button

#### Volume Management
1. Go to **Volumes** tab in Docker Desktop
2. You'll see:
   - `postgres_data` (database files)
   - `odoo_data` (Odoo file storage)

### Useful Commands

#### Check container status:
```bash
docker-compose ps
```

#### View logs:
```bash
docker-compose logs -f odoo
```

#### Stop everything:
```bash
docker-compose down
```

#### Restart Odoo only:
```bash
docker-compose restart odoo
```

#### Complete reset (removes all data):
```bash
docker-compose down -v
docker-compose up --build -d
```

### Troubleshooting 🔧

#### If containers won't start:
1. Check Docker Desktop is running
2. Ensure ports 8069, 8072, 5432 are free
3. Check logs in Docker Desktop

#### If Odoo shows database error:
1. Wait 2-3 minutes for PostgreSQL to initialize
2. Check postgres container logs
3. Restart odoo container

#### Performance issues:
1. Increase Docker Desktop memory to 6GB+
2. Close other applications
3. Check CPU usage in Docker Desktop

### Port Mapping 🌐
- **8069** → Odoo Web Interface (instead of 7981)
- **8072** → Odoo Longpolling (instead of 7982)
- **5432** → PostgreSQL Database

### Docker Desktop Features You Can Use 📊
- **Resource Usage**: Monitor CPU/Memory in real-time
- **Container Logs**: View application logs easily
- **Volume Browser**: Explore stored data
- **Network Inspector**: Check container connectivity
- **Image Management**: View and clean up images

### Success Indicators ✅
1. Both containers show "Running" status
2. Odoo logs show "odoo.service.server: HTTP service (werkzeug) running"
3. http://localhost:8069 loads the Odoo login page
4. No error messages in container logs

### Next Steps After Deployment 🚀
1. Login to Odoo with admin credentials
2. Check all custom modules are loaded
3. Verify database connectivity
4. Test core functionality
5. Configure any additional settings needed

### Backup Your Work 💾
```bash
# Database backup
docker-compose exec postgres pg_dump -U odooprod odoo_production > odoo_backup.sql

# Volume backup (using Docker Desktop)
# Go to Volumes → Right-click volume → Export
```

This setup is optimized for Docker Desktop's interface and provides easy management through the GUI!
